#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaLiveStrategy : Strategy
    {
        // Variables for Camarilla levels
        private double h4;
        private double l4;

        // EMA indicator
        private EMA ema;



        // Variables for trailing stop
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;
        private double currentTrailStop = 0;  // Aktuálna trailing stop úroveň

        // Cache for daily H4/L4 values to avoid recalculating on every tick
        private Dictionary<DateTime, Tuple<double, double>> dailyLevelsCache = new Dictionary<DateTime, Tuple<double, double>>();

        // Custom log method to write to file
        private void LogToFile(string message)
        {
            try
            {
                string logPath = @"C:\Users\<USER>\Documents\augment-projects\Ninja bot\camarilla_log.txt";
                using (System.IO.StreamWriter sw = System.IO.File.AppendText(logPath))
                {
                    sw.WriteLine($"{DateTime.Now}: [{Instrument.FullName}] {message}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error writing to log file: {ex.Message}");
            }
        }



        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaLiveStrategy";
                Description = "Live trading strategy for Camarilla levels with real-time trailing";
                Calculate = Calculate.OnEachTick; // Use OnEachTick for real-time trailing
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.High; // PRIDANÉ PRE BACKTEST
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = true;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Default trail parameters - matched with Pine Script
                LongTrailPoints = 40;
                LongTrailOffset = 1;
                LongStopLoss = 70;
                ShortTrailOffset = 1;
                ShortTrailPoints = 20;
                ShortStopLoss = 40;

                // DÔLEŽITÉ: Pre live trading použite SetTrailStop()
                // Pre backtest trailing stops nefungujú správne

                // Default parameter values
                EmaPeriod = 8;
            }
            else if (State == State.Configure)
            {
                // Add EMA indicator
                ema = EMA(EmaPeriod);
                AddChartIndicator(ema);


            }
        }

        private bool entryEvaluatedThisBar = false;

        // Method to calculate Camarilla levels for a given date
        private void CalculateCamarillaLevels(DateTime currentDate)
        {
            // Initialize variables
            double h4 = 0;
            double l4 = 0;
            bool foundPrevDay = false;
            double prevDayHigh = double.MinValue;
            double prevDayLow = double.MaxValue;
            double prevDayClose = 0;

            // Log the date we're calculating for
            Print($"Calculating Camarilla levels for date: {currentDate:yyyy-MM-dd}, CurrentBar: {CurrentBar}");
            LogToFile($"Calculating Camarilla levels for date: {currentDate:yyyy-MM-dd}, CurrentBar: {CurrentBar}");

            // Debug all available bars
            LogToFile("Debugging all available bars for date calculation:");
            LogToFile($"Bar 0: Time={Time[0]}, Date={Time[0].Date}, Open={Open[0]}, High={High[0]}, Low={Low[0]}, Close={Close[0]}");

            // Určenie predchádzajúceho obchodného dňa podľa CME obchodných hodín
            // Pre CME futures obchodný deň začína o 18:00 predchádzajúceho dňa a končí o 17:00 aktuálneho dňa (NYC čas)
            DateTime prevDate;

            // Určenie predchádzajúceho obchodného dňa podľa tabuľky CME
            switch (currentDate.DayOfWeek)
            {
                case DayOfWeek.Monday:
                    // Pre pondelok - Camarilla úrovne sa počítajú z piatkového denného baru
                    prevDate = currentDate.AddDays(-3); // Piatok
                    break;
                case DayOfWeek.Tuesday:
                    // Pre utorok - Camarilla úrovne sa počítajú z pondelkového denného baru
                    prevDate = currentDate.AddDays(-1); // Pondelok
                    break;
                case DayOfWeek.Wednesday:
                    // Pre stredu - Camarilla úrovne sa počítajú z utorkového denného baru
                    prevDate = currentDate.AddDays(-1); // Utorok
                    break;
                case DayOfWeek.Thursday:
                    // Pre štvrtok - Camarilla úrovne sa počítajú zo stredajšieho denného baru
                    prevDate = currentDate.AddDays(-1); // Streda
                    break;
                case DayOfWeek.Friday:
                    // Pre piatok - Camarilla úrovne sa počítajú zo štvrtkovho denného baru
                    prevDate = currentDate.AddDays(-1); // Štvrtok
                    break;
                default:
                    // Pre víkend (sobota, nedeľa) - neobchodné dni, ale pre istotu
                    prevDate = currentDate.AddDays(-1);
                    if (prevDate.DayOfWeek == DayOfWeek.Saturday)
                        prevDate = currentDate.AddDays(-2); // Piatok
                    else if (prevDate.DayOfWeek == DayOfWeek.Sunday)
                        prevDate = currentDate.AddDays(-3); // Piatok
                    break;
            }

            LogToFile($"CME Trading Day - Current date: {currentDate:yyyy-MM-dd} ({currentDate.DayOfWeek}), Previous trading day: {prevDate:yyyy-MM-dd} ({prevDate.DayOfWeek})");

            // Nájdeme všetky bary pre predchádzajúci obchodný deň
            // Použijeme SessionIterator pre presné určenie CME obchodných hodín
            DateTime tradingDayStart = DateTime.MinValue;
            DateTime tradingDayEnd = DateTime.MaxValue;

            try
            {
                // Vytvoríme SessionIterator pre predchádzajúci obchodný deň
                SessionIterator sessionIterator = new SessionIterator(Bars.TradingHours);

                // Nastavíme na predchádzajúci obchodný deň
                sessionIterator.GetNextSession(prevDate, false);

                if (sessionIterator.IsInSession(prevDate, false, true))
                {
                    // Ak je prevDate v session, použijeme túto session
                    tradingDayStart = sessionIterator.ActualSessionBegin;
                    tradingDayEnd = sessionIterator.ActualSessionEnd;
                }
                else
                {
                    // Ak nie, nájdeme predchádzajúcu session
                    sessionIterator.GetNextSession(prevDate.AddDays(-1), false);
                    tradingDayStart = sessionIterator.ActualSessionBegin;
                    tradingDayEnd = sessionIterator.ActualSessionEnd;
                }

                LogToFile($"SessionIterator found trading session: {tradingDayStart:yyyy-MM-dd HH:mm:ss} to {tradingDayEnd:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                // Fallback na manuálne časy ak SessionIterator zlyhá
                LogToFile($"SessionIterator failed: {ex.Message}, using manual times");
                tradingDayStart = prevDate.AddDays(-1).Date.AddHours(18); // (X-2) 18:00
                tradingDayEnd = prevDate.Date.AddHours(17); // (X-1) 17:00
                LogToFile($"Fallback to manual trading day: {tradingDayStart:yyyy-MM-dd HH:mm} to {tradingDayEnd:yyyy-MM-dd HH:mm}");
            }

            List<int> barsForPrevDay = new List<int>();
            for (int i = 0; i < Math.Min(CurrentBar, 1000); i++)
            {
                DateTime barTime = Time[i];
                // Bar patrí do obchodného dňa ak je medzi 18:00 predchádzajúceho dňa a 17:00 aktuálneho dňa
                if (barTime >= tradingDayStart && barTime <= tradingDayEnd)
                {
                    barsForPrevDay.Add(i);
                    foundPrevDay = true;
                }
            }

            // Ak sme našli bary pre predchádzajúci deň, spracujeme ich
            if (barsForPrevDay.Count > 0)
            {
                // Detailné logovanie všetkých barov pre predchádzajúci deň
                LogToFile($"Found {barsForPrevDay.Count} bars for previous day {prevDate:yyyy-MM-dd}:");
                foreach (int barIndex in barsForPrevDay)
                {
                    LogToFile($"  Bar index {barIndex}: Time={Time[barIndex]}, Open={Open[barIndex]}, High={High[barIndex]}, Low={Low[barIndex]}, Close={Close[barIndex]}");
                }

                // Nájdeme high/low pre všetky bary
                foreach (int barIndex in barsForPrevDay)
                {
                    if (prevDayHigh < High[barIndex])
                    {
                        prevDayHigh = High[barIndex];
                        LogToFile($"New HIGH found: {prevDayHigh} at bar index {barIndex}, Time: {Time[barIndex]}");
                    }
                    if (prevDayLow > Low[barIndex])
                    {
                        prevDayLow = Low[barIndex];
                        LogToFile($"New LOW found: {prevDayLow} at bar index {barIndex}, Time: {Time[barIndex]}");
                    }
                }

                // DÔLEŽITÉ: Close hodnota musí byť z posledného baru obchodného dňa
                // Pre CME futures obchodný deň končí o 17:00 EST (22:00 UTC)
                // Musíme nájsť bar s časom 17:00 (alebo najbližší k tomu)
                int lastBarOfDay = -1;
                DateTime targetEndTime = prevDate.Date.AddHours(17); // 17:00 EST

                // Nájdeme bar s časom 17:00 alebo najbližší k tomu
                foreach (int barIndex in barsForPrevDay)
                {
                    DateTime barTime = Time[barIndex];
                    if (barTime.Hour == 17 && barTime.Minute == 0)
                    {
                        lastBarOfDay = barIndex;
                        break;
                    }
                }

                // Ak sme nenašli presný čas 17:00, vezmeme posledný bar pred 18:00
                if (lastBarOfDay == -1)
                {
                    foreach (int barIndex in barsForPrevDay.OrderBy(x => x))
                    {
                        DateTime barTime = Time[barIndex];
                        if (barTime.Hour < 18)
                        {
                            lastBarOfDay = barIndex;
                        }
                        else
                        {
                            break; // Už sme prešli za 18:00
                        }
                    }
                }

                // Ak stále nemáme posledný bar, vezmeme najstarší bar (najvyšší index)
                if (lastBarOfDay == -1)
                {
                    lastBarOfDay = barsForPrevDay.Max();
                }

                prevDayClose = Close[lastBarOfDay];

                LogToFile($"Last bar of day index: {lastBarOfDay}, Time: {Time[lastBarOfDay]}, Close: {prevDayClose}");
                LogToFile($"All bar indices for previous day: [{string.Join(", ", barsForPrevDay.OrderBy(x => x))}]");
            }

            // Check if we have valid data
            if (foundPrevDay)
            {
                // Log all collected data for the previous day
                LogToFile($"FINAL DATA FOR PREVIOUS DAY: High={prevDayHigh}, Low={prevDayLow}, Close={prevDayClose}");

                // Camarilla formula for H4/L4
                // Správny vzorec pre Camarilla úrovne
                double range = prevDayHigh - prevDayLow;

                // Pôvodný vzorec: H4 = C + (H - L) * 1.1/2, L4 = C - (H - L) * 1.1/2
                double h4Multiplier = 1.1 / 2.0;
                double l4Multiplier = 1.1 / 2.0;

                // Výpočet H4/L4
                h4 = prevDayClose + (range * h4Multiplier);
                l4 = prevDayClose - (range * l4Multiplier);

                // Žiadne korekcie - používame štandardný vzorec Camarilla
                LogToFile($"Using standard Camarilla formula without corrections");

                // Zaokrúhlenie na 2 desatinné miesta
                h4 = Math.Round(h4 * 100) / 100;
                l4 = Math.Round(l4 * 100) / 100;

                // Log výpočtových krokov
                LogToFile($"Calculation: Range={range}, H4 Multiplier={h4Multiplier}, L4 Multiplier={l4Multiplier}");
                LogToFile($"H4 = {prevDayClose} + ({range} * {h4Multiplier}) = {h4}");
                LogToFile($"L4 = {prevDayClose} - ({range} * {l4Multiplier}) = {l4}");

                // Log the final values
                LogToFile($"FINAL H4/L4 VALUES: H4={h4}, L4={l4}");

                string calcInfo = $"Calculated H4/L4 for {currentDate:yyyy-MM-dd} based on previous day: H4={h4}, L4={l4}, PrevHigh={prevDayHigh}, PrevLow={prevDayLow}, PrevClose={prevDayClose}";
                Print(calcInfo);
                LogToFile(calcInfo);

                // Cache the calculated values
                dailyLevelsCache[currentDate] = new Tuple<double, double>(h4, l4);

                // DÔLEŽITÉ: Pridáme aj záznam pre predchádzajúci deň, aby sme mali správne hodnoty
                // Toto je kľúčová oprava - úrovne sa zobrazujú pre predchádzajúci deň, nie pre aktuálny
                dailyLevelsCache[prevDate] = new Tuple<double, double>(h4, l4);
                LogToFile($"IMPORTANT: Also caching H4/L4 values for previous day {prevDate:yyyy-MM-dd}: H4={h4}, L4={l4}");
            }
            else
            {
                // Default values if we can't find previous day's data
                h4 = 5900;
                l4 = 5800;
                string defaultInfo = $"Using default H4/L4 values for {currentDate:yyyy-MM-dd} - could not find previous day's data";
                Print(defaultInfo);
                LogToFile(defaultInfo);

                // Cache the default values
                dailyLevelsCache[currentDate] = new Tuple<double, double>(h4, l4);

                // DÔLEŽITÉ: Pridáme aj záznam pre predchádzajúci deň, aby sme mali správne hodnoty
                dailyLevelsCache[prevDate] = new Tuple<double, double>(h4, l4);
                LogToFile($"IMPORTANT: Also caching default H4/L4 values for previous day {prevDate:yyyy-MM-dd}: H4={h4}, L4={l4}");
            }
        }

        protected override void OnBarUpdate()
        {
            try
            {
                // Skip processing if we don't have enough bars - REDUCED TO MINIMUM
                if (BarsInProgress != 0 || CurrentBar < 1) // Only need 1 bar
                    return;

                // Debug info - print every bar
                if (IsFirstTickOfBar)
                {
                    string barInfo = $"Processing bar: {CurrentBar}, Time: {Time[0]}, Position: {Position.MarketPosition}";
                    Print(barInfo);
                    LogToFile(barInfo);
                    // Reset entry flag at the start of a new bar
                    entryEvaluatedThisBar = false;
                }

                // Get current bar date
                DateTime currentDate = Time[0].Date;

                // Check if we already calculated H4/L4 for this date
                if (!dailyLevelsCache.ContainsKey(currentDate))
                {
                    // Calculate H4/L4 values dynamically from previous day's data
                    CalculateCamarillaLevels(currentDate);
                }
                else
                {
                    // Use cached values
                    var levels = dailyLevelsCache[currentDate];
                    h4 = levels.Item1;
                    l4 = levels.Item2;
                }

                // Draw H4/L4 levels on first bar of the day or when the date changes
                // OPRAVA: Kreslíme len pre MES, nie pre M6E aby sme predišli dvojitému kresleniu
                if ((CurrentBar == 0 || Time[1].Date != currentDate) && Instrument.FullName.Contains("MES"))
                {
                    string dateStr = currentDate.ToString("yyyyMMdd");

                    // Remove old lines if they exist
                    RemoveDrawObject("H4_" + dateStr);
                    RemoveDrawObject("L4_" + dateStr);

                    // OPRAVA: Používame hodnoty pre aktuálny deň, ktoré sú už vypočítané z predchádzajúceho dňa
                    // Tieto hodnoty sú už správne, pretože sme ich vypočítali z predchádzajúceho dňa
                    double currentH4 = h4;
                    double currentL4 = l4;

                    // Získame hodnoty z cache pre aktuálny deň
                    if (dailyLevelsCache.ContainsKey(currentDate))
                    {
                        var currentLevels = dailyLevelsCache[currentDate];
                        currentH4 = currentLevels.Item1;
                        currentL4 = currentLevels.Item2;
                    }

                    LogToFile($"Using H4/L4 values for current day {currentDate:yyyy-MM-dd}: H4={currentH4}, L4={currentL4}");

                    // Draw horizontal lines for H4/L4 with day boundaries
                    // Calculate start and end points for the day
                    DateTime dayStart = Time[0].Date;
                    DateTime dayEnd = dayStart.AddDays(1).AddSeconds(-1);

                    // Draw H4/L4 lines with day boundaries
                    Draw.Line(this, "H4_" + dateStr, false, dayStart, currentH4, dayEnd, currentH4, Brushes.Orange, DashStyleHelper.Solid, 2);
                    Draw.Line(this, "L4_" + dateStr, false, dayStart, currentL4, dayEnd, currentL4, Brushes.Orange, DashStyleHelper.Solid, 2);

                    // Add text labels
                    Draw.Text(this, "H4_Text_" + dateStr, "H4: " + currentH4.ToString("0.00"), 0, currentH4 + 5 * TickSize, Brushes.Orange);
                    Draw.Text(this, "L4_Text_" + dateStr, "L4: " + currentL4.ToString("0.00"), 0, currentL4 - 5 * TickSize, Brushes.Orange);

                    // Log the drawing of lines
                    LogToFile($"Drawing H4/L4 lines for {currentDate:yyyy-MM-dd}: H4={currentH4}, L4={currentL4}");
                }

                // CAMARILLA ENTRY CONDITIONS
                // Potrebujeme použiť hodnoty z predchádzajúceho dňa pre vstupné podmienky
                double entryH4 = h4;
                double entryL4 = l4;

                // Určenie predchádzajúceho obchodného dňa podľa CME obchodných hodín
                // Pre CME futures obchodný deň začína o 18:00 predchádzajúceho dňa a končí o 17:00 aktuálneho dňa (NYC čas)
                DateTime prevDate;

                // Určenie predchádzajúceho obchodného dňa podľa tabuľky CME
                switch (currentDate.DayOfWeek)
                {
                    case DayOfWeek.Monday:
                        // Pre pondelok - Camarilla úrovne sa počítajú z piatkového denného baru
                        prevDate = currentDate.AddDays(-3); // Piatok
                        break;
                    case DayOfWeek.Tuesday:
                        // Pre utorok - Camarilla úrovne sa počítajú z pondelkového denného baru
                        prevDate = currentDate.AddDays(-1); // Pondelok
                        break;
                    case DayOfWeek.Wednesday:
                        // Pre stredu - Camarilla úrovne sa počítajú z utorkového denného baru
                        prevDate = currentDate.AddDays(-1); // Utorok
                        break;
                    case DayOfWeek.Thursday:
                        // Pre štvrtok - Camarilla úrovne sa počítajú zo stredajšieho denného baru
                        prevDate = currentDate.AddDays(-1); // Streda
                        break;
                    case DayOfWeek.Friday:
                        // Pre piatok - Camarilla úrovne sa počítajú zo štvrtkovho denného baru
                        prevDate = currentDate.AddDays(-1); // Štvrtok
                        break;
                    default:
                        // Pre víkend (sobota, nedeľa) - neobchodné dni, ale pre istotu
                        prevDate = currentDate.AddDays(-1);
                        if (prevDate.DayOfWeek == DayOfWeek.Saturday)
                            prevDate = currentDate.AddDays(-2); // Piatok
                        else if (prevDate.DayOfWeek == DayOfWeek.Sunday)
                            prevDate = currentDate.AddDays(-3); // Piatok
                        break;
                }

                // Logujeme informácie o vstupných podmienkach len pri prvom ticku baru alebo pri zmene dňa
                if (IsFirstTickOfBar || (CurrentBar > 0 && Time[1].Date != currentDate))
                {
                    LogToFile($"Entry conditions - Current date: {currentDate:yyyy-MM-dd} ({currentDate.DayOfWeek}), Previous trading day: {prevDate:yyyy-MM-dd} ({prevDate.DayOfWeek})");
                }

                if (dailyLevelsCache.ContainsKey(prevDate))
                {
                    var prevLevels = dailyLevelsCache[prevDate];
                    entryH4 = prevLevels.Item1;
                    entryL4 = prevLevels.Item2;

                    // Logujeme informácie o H4/L4 hodnotách len pri prvom ticku baru alebo pri zmene dňa
                    if (IsFirstTickOfBar || (CurrentBar > 0 && Time[1].Date != currentDate))
                    {
                        LogToFile($"Using entry H4/L4 values from previous day {prevDate:yyyy-MM-dd}: H4={entryH4}, L4={entryL4}");
                    }
                }

                // Long entry: Price crosses above H4
                bool longCondition1 = CrossAbove(Close, entryH4, 1);
                bool longCondition2 = Close[0] > entryH4;
                bool longCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // Short entry: Price crosses below L4
                bool shortCondition1 = CrossBelow(Close, entryL4, 1);
                bool shortCondition2 = Close[0] < entryL4;
                bool shortCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // Entry logic - ONLY EVALUATE ON BAR CLOSE
                // This ensures entries happen like in TradingView (only at bar close)
                if (!entryEvaluatedThisBar && Calculate == Calculate.OnBarClose ||
                    !entryEvaluatedThisBar && Calculate == Calculate.OnEachTick && IsFirstTickOfBar)
                {
                    // Long entry
                    if (longCondition1 && longCondition2 && longCondition3)
                    {
                        EnterLong(1, "Long");
                        entryPrice = Close[0];
                        highestSinceEntry = Close[0];
                        longTrailingActive = false;
                        currentTrailStop = 0; // Reset trailing stop
                        Draw.ArrowUp(this, "LongEntry_" + CurrentBar, true, 0, Low[0] - 10 * TickSize, Brushes.Green);
                        Draw.Text(this, "LongEntryText_" + CurrentBar, "LONG", 0, Low[0] - 20 * TickSize, Brushes.Green);
                        Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                    }
                    // Short entry
                    else if (shortCondition1 && shortCondition2 && shortCondition3)
                    {
                        EnterShort(1, "Short");
                        entryPrice = Close[0];
                        lowestSinceEntry = Close[0];
                        shortTrailingActive = false;
                        currentTrailStop = 0; // Reset trailing stop
                        Draw.ArrowDown(this, "ShortEntry_" + CurrentBar, true, 0, High[0] + 10 * TickSize, Brushes.Red);
                        Draw.Text(this, "ShortEntryText_" + CurrentBar, "SHORT", 0, High[0] + 20 * TickSize, Brushes.Red);
                        Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                    }

                    // Mark that we've evaluated entries for this bar
                    entryEvaluatedThisBar = true;
                }

                // Trailing stop logic - MUSÍ SA VYHODNOCOVAŤ NA KAŽDOM TICKU, NIE LEN PRI BAR CLOSE
                // Manage trailing stop for long
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    // Update highest price since entry - kontrolujeme aj High[0] a aktuálnu cenu
                    double currentHigh = Math.Max(High[0], Close[0]);
                    if (currentHigh > highestSinceEntry)
                    {
                        highestSinceEntry = currentHigh;
                        LogToFile($"LONG: Updated highest since entry to {highestSinceEntry}, Current price: {Close[0]}, High[0]: {High[0]}");
                    }

                    // Activate trailing stop only after price moves LongTrailPoints ticks in our favor
                    double trailActivationPrice = entryPrice + (LongTrailPoints * TickSize);

                    if (!longTrailingActive)
                    {
                        LogToFile($"LONG TRAIL CHECK: Entry={entryPrice}, Current={Close[0]}, High={High[0]}, Activation needed={trailActivationPrice}, Points={LongTrailPoints}");

                        if (Close[0] >= trailActivationPrice || High[0] >= trailActivationPrice)
                        {
                            longTrailingActive = true;
                            string activationMsg = $"LONG TRAIL ACTIVATED: Price={Close[0]}, High={High[0]}, Entry={entryPrice}, Activation={trailActivationPrice}, Points={LongTrailPoints} ticks";
                            Print(activationMsg);
                            LogToFile(activationMsg);
                        }
                    }

                    // If trailing stop is active, manage trailing stop
                    if (longTrailingActive)
                    {
                        double newStop = highestSinceEntry - (LongTrailOffset * TickSize);

                        // Aktualizujeme trailing stop len ak sa zlepšil (posunul vyššie)
                        if (newStop > currentTrailStop || currentTrailStop == 0)
                        {
                            currentTrailStop = newStop;

                            // POUŽIJEME SETSTOPLOSS PRE OKAMŽITÚ AKTUALIZÁCIU
                            SetStopLoss("Long", CalculationMode.Price, currentTrailStop, false);

                            LogToFile($"LONG TRAIL UPDATED: New stop={currentTrailStop}, Highest={highestSinceEntry}, Offset={LongTrailOffset}, Current={Close[0]}");

                            // Draw trailing stop line
                            if (IsFirstTickOfBar)
                                Draw.Line(this, "LongTrailStop", false, 0, currentTrailStop, 10, currentTrailStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                        }

                        // DETAILNÝ LOG PRE KAŽDÝ TICK
                        LogToFile($"LONG TRAIL CHECK: Current={Close[0]}, TrailStop={currentTrailStop}, Should exit={Close[0] <= currentTrailStop}");

                        // VLASTNÝ EXIT MECHANIZMUS PRE BACKTEST
                        if (Close[0] <= currentTrailStop)
                        {
                            ExitLong("LongTrail", "Long");
                            string exitMsg = $"LONG TRAIL EXIT: Price={Close[0]}, TrailStop={currentTrailStop}, Highest={highestSinceEntry}, Profit={(Close[0] - entryPrice) / TickSize} ticks";
                            Print(exitMsg);
                            LogToFile(exitMsg);

                            // Reset trailing stop
                            currentTrailStop = 0;
                            longTrailingActive = false;
                        }
                    }
                    else
                    {
                        // Fixed stop loss - len ak trailing nie je aktívny
                        double stopPrice = entryPrice - (LongStopLoss * TickSize);
                        SetStopLoss("Long", CalculationMode.Price, stopPrice, false);

                        // Draw stop loss line - only once when position is opened
                        if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                            Draw.Line(this, "LongStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);
                    }
                }

                // Manage trailing stop for short
                if (Position.MarketPosition == MarketPosition.Short)
                {
                    // Update lowest price since entry - kontrolujeme aj Low[0] a aktuálnu cenu
                    double currentLow = Math.Min(Low[0], Close[0]);
                    if (currentLow < lowestSinceEntry)
                    {
                        lowestSinceEntry = currentLow;
                        LogToFile($"SHORT: Updated lowest since entry to {lowestSinceEntry}, Current price: {Close[0]}, Low[0]: {Low[0]}");
                    }

                    // Activate trailing stop only after price moves ShortTrailPoints ticks in our favor
                    double trailActivationPrice = entryPrice - (ShortTrailPoints * TickSize);

                    if (!shortTrailingActive)
                    {
                        LogToFile($"SHORT TRAIL CHECK: Entry={entryPrice}, Current={Close[0]}, Low={Low[0]}, Activation needed={trailActivationPrice}, Points={ShortTrailPoints}");

                        if (Close[0] <= trailActivationPrice || Low[0] <= trailActivationPrice)
                        {
                            shortTrailingActive = true;
                            string activationMsg = $"SHORT TRAIL ACTIVATED: Price={Close[0]}, Low={Low[0]}, Entry={entryPrice}, Activation={trailActivationPrice}, Points={ShortTrailPoints} ticks";
                            Print(activationMsg);
                            LogToFile(activationMsg);
                        }
                    }

                    // If trailing stop is active, manage trailing stop
                    if (shortTrailingActive)
                    {
                        double newStop = lowestSinceEntry + (ShortTrailOffset * TickSize);

                        // Aktualizujeme trailing stop len ak sa zlepšil (posunul nižšie)
                        if (newStop < currentTrailStop || currentTrailStop == 0)
                        {
                            currentTrailStop = newStop;

                            // POUŽIJEME SETSTOPLOSS PRE OKAMŽITÚ AKTUALIZÁCIU
                            SetStopLoss("Short", CalculationMode.Price, currentTrailStop, false);

                            LogToFile($"SHORT TRAIL UPDATED: New stop={currentTrailStop}, Lowest={lowestSinceEntry}, Offset={ShortTrailOffset}, Current={Close[0]}");

                            // Draw trailing stop line
                            if (IsFirstTickOfBar)
                                Draw.Line(this, "ShortTrailStop", false, 0, currentTrailStop, 10, currentTrailStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                        }

                        // DETAILNÝ LOG PRE KAŽDÝ TICK
                        LogToFile($"SHORT TRAIL CHECK: Current={Close[0]}, TrailStop={currentTrailStop}, Should exit={Close[0] >= currentTrailStop}");

                        // VLASTNÝ EXIT MECHANIZMUS PRE BACKTEST
                        if (Close[0] >= currentTrailStop)
                        {
                            ExitShort("ShortTrail", "Short");
                            string exitMsg = $"SHORT TRAIL EXIT: Price={Close[0]}, TrailStop={currentTrailStop}, Lowest={lowestSinceEntry}, Profit={(entryPrice - Close[0]) / TickSize} ticks";
                            Print(exitMsg);
                            LogToFile(exitMsg);

                            // Reset trailing stop
                            currentTrailStop = 0;
                            shortTrailingActive = false;
                        }
                    }
                    else
                    {
                        // Fixed stop loss - len ak trailing nie je aktívny
                        double stopPrice = entryPrice + (ShortStopLoss * TickSize);
                        SetStopLoss("Short", CalculationMode.Price, stopPrice, false);

                        // Draw stop loss line - only once when position is opened
                        if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                            Draw.Line(this, "ShortStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarUpdate: {ex.Message}");
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="EMA Period", Description="Period for EMA indicator", Order=1, GroupName="Parameters")]
        public int EmaPeriod
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Trail Points", Description="Trail points for long positions", Order=2, GroupName="Parameters")]
        public int LongTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Long Trail Offset", Description="Trail offset for long positions", Order=3, GroupName="Parameters")]
        public int LongTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Stop Loss", Description="Stop loss for long positions", Order=4, GroupName="Parameters")]
        public int LongStopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Trail Points", Description="Trail points for short positions", Order=5, GroupName="Parameters")]
        public int ShortTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Short Trail Offset", Description="Trail offset for short positions", Order=6, GroupName="Parameters")]
        public int ShortTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Stop Loss", Description="Stop loss for short positions", Order=7, GroupName="Parameters")]
        public int ShortStopLoss
        { get; set; }
        #endregion
    }
}
