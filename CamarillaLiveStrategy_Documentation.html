<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CamarillaLiveStrategy Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        code {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            padding: 2px 4px;
            color: #c7254e;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin: 15px 0;
        }
        .warning {
            background-color: #ffffcc;
            border-left: 6px solid #ffeb3b;
            padding: 10px;
            margin: 15px 0;
        }
        .formula {
            font-style: italic;
            margin: 10px 0;
            padding-left: 20px;
        }
        .parameter-section {
            margin-bottom: 30px;
        }
        .visualization-item {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>CamarillaLiveStrategy Documentation</h1>

    <h2>Overview</h2>
    <p>
        CamarillaLiveStrategy is a professional trading strategy for NinjaTrader 8 that implements the Camarilla pivot point system.
        The strategy dynamically calculates H4 and L4 levels from previous trading day data and uses them to generate precise entry signals.
        It features advanced risk management with both trailing stops and fixed stop losses, optimized for live trading on futures markets.
    </p>

    <div class="warning">
        <strong>Important:</strong> This strategy is optimized for <strong>live trading</strong>. Trailing stops work correctly only with live data or simulation accounts.
        Backtest results may not accurately reflect trailing stop behavior due to NinjaTrader's historical data limitations.
    </div>

    <h2>How It Works</h2>
    <p>
        The strategy implements the Camarilla pivot point system, which calculates key support and resistance levels
        from the previous trading day's OHLC data. These levels (H4 and L4) serve as dynamic entry points for the current trading session.
    </p>

    <h3>Key Features:</h3>
    <ul>
        <li><strong>Dynamic Level Calculation:</strong> H4/L4 levels are calculated from actual previous day data, not hardcoded values</li>
        <li><strong>CME Trading Hours:</strong> Correctly handles CME futures trading sessions (18:00-17:00 EST)</li>
        <li><strong>Weekend Handling:</strong> Automatically adjusts for Monday sessions using Friday's data</li>
        <li><strong>Real-time Trailing Stops:</strong> Advanced trailing stop mechanism that activates based on favorable price movement</li>
        <li><strong>Session Iterator:</strong> Uses NinjaTrader's SessionIterator for robust timezone handling</li>
    </ul>

    <h3>Trading Logic:</h3>
    <p>
        The strategy enters a <strong>long position</strong> when price crosses above the H4 level, and a
        <strong>short position</strong> when price crosses below the L4 level. Entry conditions are evaluated
        at bar close to match TradingView behavior.
    </p>

    <h2>Parameters</h2>
    <div class="parameter-section">
        <table>
            <tr>
                <th>Parameter</th>
                <th>Description</th>
                <th>Default Value</th>
            </tr>
            <tr>
                <td>EmaPeriod</td>
                <td>Period for EMA indicator</td>
                <td>8</td>
            </tr>
            <tr>
                <td>LongTrailPoints</td>
                <td>Number of ticks price must move in your favor to activate trailing stop for long positions</td>
                <td>40</td>
            </tr>
            <tr>
                <td>LongTrailOffset</td>
                <td>Offset for trailing stop for long positions (in ticks)</td>
                <td>1</td>
            </tr>
            <tr>
                <td>LongStopLoss</td>
                <td>Stop loss for long positions (in ticks)</td>
                <td>70</td>
            </tr>
            <tr>
                <td>ShortTrailPoints</td>
                <td>Number of ticks price must move in your favor to activate trailing stop for short positions</td>
                <td>20</td>
            </tr>
            <tr>
                <td>ShortTrailOffset</td>
                <td>Offset for trailing stop for short positions (in ticks)</td>
                <td>1</td>
            </tr>
            <tr>
                <td>ShortStopLoss</td>
                <td>Stop loss for short positions (in ticks)</td>
                <td>40</td>
            </tr>
        </table>
    </div>

    <h2>Camarilla Level Calculation</h2>
    <p>
        The strategy calculates H4 and L4 levels using the standard Camarilla formula:
    </p>
    <div class="formula">
        <p>H4 = Close + (High - Low) * 1.1/2</p>
        <p>L4 = Close - (High - Low) * 1.1/2</p>
    </div>
    <p>
        Where Close, High, and Low are values from the previous trading day.
    </p>

    <h2>Entry Conditions</h2>
    <ul>
        <li><strong>Long Entry</strong>: Price crosses above H4 level (CrossAbove) and Close > H4</li>
        <li><strong>Short Entry</strong>: Price crosses below L4 level (CrossBelow) and Close < L4</li>
    </ul>
    <p>
        Entry conditions are evaluated only at bar close or at the first tick of a bar,
        to ensure behavior similar to TradingView.
    </p>

    <h2>Risk Management</h2>
    <p>
        The strategy implements a sophisticated dual-layer risk management system designed to protect capital while maximizing profit potential:
    </p>

    <h3>1. Fixed Stop Loss (Backup Protection)</h3>
    <ul>
        <li><strong>Long positions</strong>: Entry price - (LongStopLoss * TickSize)</li>
        <li><strong>Short positions</strong>: Entry price + (ShortStopLoss * TickSize)</li>
    </ul>
    <p>Fixed stop loss acts as a safety net when trailing stops are not yet active.</p>

    <h3>2. Advanced Trailing Stop System</h3>
    <div class="note">
        <strong>Trail Points vs Trail Offset:</strong>
        <ul>
            <li><strong>Trail Points:</strong> How many ticks price must move in your favor to <em>activate</em> the trailing stop</li>
            <li><strong>Trail Offset:</strong> How many ticks the stop loss <em>trails behind</em> the best price once activated</li>
        </ul>
    </div>

    <h4>Long Position Trailing:</h4>
    <ul>
        <li><strong>Activation:</strong> When price rises by LongTrailPoints (40) ticks above entry</li>
        <li><strong>Trailing:</strong> Stop loss follows at highest price - (LongTrailOffset * TickSize)</li>
        <li><strong>Example:</strong> Entry at 5900, activates at 5910, trails 1 tick behind highest price</li>
    </ul>

    <h4>Short Position Trailing:</h4>
    <ul>
        <li><strong>Activation:</strong> When price falls by ShortTrailPoints (20) ticks below entry</li>
        <li><strong>Trailing:</strong> Stop loss follows at lowest price + (ShortTrailOffset * TickSize)</li>
        <li><strong>Example:</strong> Entry at 5900, activates at 5895, trails 1 tick above lowest price</li>
    </ul>

    <div class="warning">
        <strong>Live Trading vs Backtest:</strong> Trailing stops use NinjaTrader's native SetTrailStop() method,
        which works perfectly in live trading but has limitations in historical backtests. For accurate trailing stop
        testing, use simulation accounts with live data.
    </div>

    <h2>Visualization</h2>
    <p>
        The strategy displays the following elements on the chart:
    </p>
    <ul>
        <li class="visualization-item"><strong>Orange horizontal lines</strong>: H4 and L4 levels for the current day</li>
        <li class="visualization-item"><strong>Red dashed lines</strong>: Fixed stop loss</li>
        <li class="visualization-item"><strong>Blue dashed lines</strong>: Trailing stop</li>
        <li class="visualization-item"><strong>Green arrows</strong>: Long entries</li>
        <li class="visualization-item"><strong>Red arrows</strong>: Short entries</li>
    </ul>

    <h2>Special Features</h2>
    <h3>Caching Levels</h3>
    <p>
        The strategy caches calculated H4/L4 levels to avoid recalculating them on every tick.
    </p>

    <h3>Weekend Handling</h3>
    <p>
        The strategy correctly identifies the previous trading day, even when the current day is Monday
        (it takes data from Friday).
    </p>

    <h3>Logging</h3>
    <p>
        The strategy writes important information to a log file for debugging purposes.
    </p>

    <h2>Installation and Usage</h2>

    <h3>Prerequisites</h3>
    <ul>
        <li>NinjaTrader 8 (latest version recommended)</li>
        <li>Market data subscription for target instruments (MES, MGC, etc.)</li>
        <li>For live trading: Broker connection with futures trading enabled</li>
    </ul>

    <h3>Installation Steps</h3>
    <ol>
        <li>Copy the CamarillaLiveStrategy.cs file to your NinjaTrader strategies folder</li>
        <li>Compile the strategy in NinjaTrader (Tools → Edit NinjaScript → Strategy)</li>
        <li>Verify compilation is successful with no errors</li>
        <li>Add the strategy to a chart with your desired instrument and timeframe</li>
    </ol>

    <h3>Recommended Settings</h3>
    <table>
        <tr>
            <th>Setting</th>
            <th>Recommended Value</th>
            <th>Purpose</th>
        </tr>
        <tr>
            <td>Timeframe</td>
            <td>30 minutes</td>
            <td>Optimal balance between signal quality and frequency</td>
        </tr>
        <tr>
            <td>Instruments</td>
            <td>MES, MGC</td>
            <td>Tested and optimized for these CME micro futures</td>
        </tr>
        <tr>
            <td>Calculate</td>
            <td>OnEachTick</td>
            <td>Required for real-time trailing stops</td>
        </tr>
        <tr>
            <td>Max Positions</td>
            <td>1 per instrument</td>
            <td>Risk management and position control</td>
        </tr>
    </table>

    <h3>Testing Workflow</h3>
    <ol>
        <li><strong>Backtest:</strong> Use Strategy Analyzer for initial parameter testing (note trailing stop limitations)</li>
        <li><strong>Simulation:</strong> Test with live data on simulation account to verify trailing stops</li>
        <li><strong>Live Trading:</strong> Start with small position sizes and monitor performance</li>
    </ol>

    <h2>Technical Notes</h2>

    <h3>Backtest Limitations</h3>
    <div class="warning">
        <strong>Important Limitation:</strong> NinjaTrader's backtest engine processes historical data differently than live data:
        <ul>
            <li>Historical data: OnBarUpdate() called only once per bar (at bar close)</li>
            <li>Live data: OnBarUpdate() called on every tick when Calculate = OnEachTick</li>
            <li>Result: Trailing stops may not execute properly in backtests, even with High Fill Resolution</li>
        </ul>
    </div>

    <h3>Live Trading Behavior</h3>
    <div class="note">
        <strong>Live Trading Advantages:</strong>
        <ul>
            <li>Trailing stops work exactly as designed with real-time tick data</li>
            <li>Entry conditions evaluated properly at bar close</li>
            <li>All risk management features function correctly</li>
            <li>Logging provides detailed execution information</li>
        </ul>
    </div>

    <h3>Logging and Debugging</h3>
    <p>
        The strategy writes detailed logs to: <code>C:\Users\<USER>\Documents\augment-projects\Ninja bot\camarilla_log.txt</code>
    </p>
    <p>Log information includes:</p>
    <ul>
        <li>H4/L4 level calculations and sources</li>
        <li>Entry condition evaluations</li>
        <li>Trailing stop activation and updates</li>
        <li>Position management events</li>
    </ul>

    <h2>Performance Optimization</h2>

    <h3>Parameter Tuning</h3>
    <ul>
        <li><strong>Trail Points:</strong> Adjust based on instrument volatility and average daily range</li>
        <li><strong>Trail Offset:</strong> Balance between profit protection and premature exits</li>
        <li><strong>Stop Loss:</strong> Set based on risk tolerance and account size</li>
    </ul>

    <h3>Market Conditions</h3>
    <ul>
        <li><strong>Trending Markets:</strong> Strategy performs best with clear directional moves</li>
        <li><strong>Range-bound Markets:</strong> May generate more false signals</li>
        <li><strong>High Volatility:</strong> Consider adjusting trail points and stop losses</li>
    </ul>
</body>
</html>
